# coding: utf-8
"""
用户对话框模块

该模块定义了用户添加和编辑对话框，提供用户信息的输入和修改功能。

主要功能：
- 用户信息输入和验证
- 支持添加和编辑模式
- 表单验证和错误提示

类说明：
- UserDialog: 用户添加/编辑对话框

作者: 小帅工具箱
版本: v1.0
"""

import re
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel
from qfluentwidgets import (LineEdit, PasswordLineEdit, PrimaryPushButton, PushButton,
                            BodyLabel, SubtitleLabel, MessageBox, InfoBar, InfoBarPosition,
                            FluentIcon as FIF)

from ...database.models import User
from ...database.dao import user_dao


class UserDialog(QDialog):
    """
    用户添加/编辑对话框
    
    提供用户信息的输入和修改功能，支持添加新用户和编辑现有用户。
    """
    
    # 信号定义
    userSaved = Signal(User)  # 用户保存成功信号
    
    def __init__(self, parent=None, user: User = None):
        """
        初始化用户对话框
        
        Args:
            parent: 父窗口
            user: 要编辑的用户对象，None表示添加新用户
        """
        super().__init__(parent)
        self.user = user
        self.is_edit_mode = user is not None
        
        self.setWindowTitle("编辑用户" if self.is_edit_mode else "添加用户")
        self.setFixedSize(400, 350)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        self.initUI()
        self.connectSignalToSlot()
        
        if self.is_edit_mode:
            self.loadUserData()
    
    def initUI(self):
        """初始化用户界面"""
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(30, 30, 30, 30)
        self.vBoxLayout.setSpacing(20)
        
        # 标题
        self.titleLabel = SubtitleLabel(
            "编辑用户信息" if self.is_edit_mode else "添加新用户",
            self
        )
        self.vBoxLayout.addWidget(self.titleLabel)
        
        # 表单布局
        self.formLayout = QFormLayout()
        self.formLayout.setSpacing(15)
        
        # 手机号输入
        self.phoneEdit = LineEdit(self)
        self.phoneEdit.setPlaceholderText("请输入11位手机号")
        self.phoneEdit.setMaxLength(11)
        if self.is_edit_mode:
            self.phoneEdit.setEnabled(False)  # 编辑模式下手机号不可修改
        self.formLayout.addRow("手机号:", self.phoneEdit)
        
        # 密码输入
        self.passwordEdit = PasswordLineEdit(self)
        self.passwordEdit.setPlaceholderText("请输入密码")
        self.formLayout.addRow("密码:", self.passwordEdit)
        
        # 姓名输入
        self.nameEdit = LineEdit(self)
        self.nameEdit.setPlaceholderText("请输入姓名（可选）")
        self.formLayout.addRow("姓名:", self.nameEdit)
        
        # 状态标签（仅编辑模式显示）
        if self.is_edit_mode:
            self.statusLabel = BodyLabel("", self)
            self.formLayout.addRow("当前状态:", self.statusLabel)
            
            self.progressLabel = BodyLabel("", self)
            self.formLayout.addRow("学习进度:", self.progressLabel)
        
        self.vBoxLayout.addLayout(self.formLayout)
        
        # 按钮布局
        self.buttonLayout = QHBoxLayout()
        self.buttonLayout.setSpacing(15)
        
        self.cancelBtn = PushButton("取消", self)
        self.saveBtn = PrimaryPushButton("保存", self)
        self.saveBtn.setIcon(FIF.SAVE)
        
        self.buttonLayout.addStretch()
        self.buttonLayout.addWidget(self.cancelBtn)
        self.buttonLayout.addWidget(self.saveBtn)
        
        self.vBoxLayout.addLayout(self.buttonLayout)
    
    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.cancelBtn.clicked.connect(self.reject)
        self.saveBtn.clicked.connect(self.onSave)
        self.phoneEdit.textChanged.connect(self.onPhoneChanged)
    
    def loadUserData(self):
        """加载用户数据（编辑模式）"""
        if self.user:
            self.phoneEdit.setText(self.user.phone)
            self.passwordEdit.setText(self.user.password)
            self.nameEdit.setText(self.user.name or "")
            
            if hasattr(self, 'statusLabel'):
                self.statusLabel.setText(self.user.status)
                
            if hasattr(self, 'progressLabel'):
                progress_text = f"{self.user.progress:.1f}% ({self.user.online_total_credit:.1f}学时)"
                self.progressLabel.setText(progress_text)
    
    def onPhoneChanged(self, text: str):
        """手机号输入改变"""
        # 只允许输入数字
        filtered_text = re.sub(r'[^\d]', '', text)
        if filtered_text != text:
            self.phoneEdit.setText(filtered_text)
    
    def validateInput(self) -> tuple[bool, str]:
        """
        验证输入数据
        
        Returns:
            tuple[bool, str]: (是否有效, 错误消息)
        """
        phone = self.phoneEdit.text().strip()
        password = self.passwordEdit.text().strip()
        name = self.nameEdit.text().strip()
        
        # 验证手机号
        if not phone:
            return False, "请输入手机号"
        
        if len(phone) != 11:
            return False, "手机号必须是11位数字"
        
        if not phone.isdigit():
            return False, "手机号只能包含数字"
        
        if not phone.startswith(('13', '14', '15', '16', '17', '18', '19')):
            return False, "请输入有效的手机号"
        
        # 验证密码
        if not password:
            return False, "请输入密码"
        
        if len(password) < 6:
            return False, "密码长度不能少于6位"
        
        # 检查手机号是否已存在（仅添加模式）
        if not self.is_edit_mode:
            existing_user = user_dao.get_user_by_phone(phone)
            if existing_user:
                return False, "该手机号已存在"
        
        return True, ""
    
    def onSave(self):
        """保存用户"""
        # 验证输入
        is_valid, error_msg = self.validateInput()
        if not is_valid:
            InfoBar.error(
                title="输入错误",
                content=error_msg,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )
            return
        
        try:
            phone = self.phoneEdit.text().strip()
            password = self.passwordEdit.text().strip()
            name = self.nameEdit.text().strip()
            
            if self.is_edit_mode:
                # 更新现有用户
                success = user_dao.update_user_basic_info(phone, {
                    'password': password,
                    'name': name
                })
                
                if success:
                    # 更新用户对象
                    self.user.password = password
                    self.user.name = name
                    
                    InfoBar.success(
                        title="保存成功",
                        content="用户信息已更新",
                        orient=Qt.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP_RIGHT,
                        duration=2000,
                        parent=self
                    )
                    
                    self.userSaved.emit(self.user)
                    self.accept()
                else:
                    raise Exception("更新用户信息失败")
            else:
                # 创建新用户
                new_user = User(
                    phone=phone,
                    password=password,
                    name=name,
                    status="未开始"
                )
                
                success = user_dao.create_user(new_user)
                
                if success:
                    InfoBar.success(
                        title="添加成功",
                        content=f"用户 {phone} 已添加",
                        orient=Qt.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP_RIGHT,
                        duration=2000,
                        parent=self
                    )
                    
                    self.userSaved.emit(new_user)
                    self.accept()
                else:
                    raise Exception("创建用户失败")
                    
        except Exception as e:
            InfoBar.error(
                title="保存失败",
                content=f"保存用户失败: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )
    
    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        # 设置焦点到第一个可编辑的输入框
        if self.is_edit_mode:
            self.passwordEdit.setFocus()
        else:
            self.phoneEdit.setFocus()
