# coding: utf-8
"""
学习设置界面模块

该模块定义了学习设置界面，提供学习工具的各项配置和参数设置功能。

主要功能：
- 系统配置设置
- 浏览器配置
- OCR配置
- API配置
- 学习策略配置

类说明：
- StudySettingInterface: 学习设置界面类

作者: 小帅工具箱
版本: v1.0
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QLabel
from qfluentwidgets import (ScrollArea, InfoBar,
                            FluentIcon as FIF,
                            OptionsSettingCard, RangeSettingCard, SwitchSettingCard,
                            ExpandLayout, SettingCardGroup)

from ..common.config import cfg


class StudySettingInterface(ScrollArea):
    """
    学习设置界面类

    提供学习工具的各项配置和参数设置功能。
    """

    def __init__(self, parent=None):
        """
        初始化学习设置界面

        Args:
            parent: 父窗口对象
        """
        super().__init__(parent=parent)
        self.scrollWidget = QWidget()
        self.expandLayout = ExpandLayout(self.scrollWidget)

        # 设置标签
        self.settingLabel = QLabel("学习设置", self)

        # 系统设置组
        self.systemGroup = SettingCardGroup("系统设置", self.scrollWidget)
        self.asyncLoginCard = SwitchSettingCard(
            FIF.SYNC,
            "异步登录",
            "启用异步登录模式，提高登录效率",
            cfg.asyncLogin,
            self.systemGroup
        )
        self.concurrentCard = RangeSettingCard(
            cfg.concurrentCount,
            FIF.PEOPLE,
            "并发数量",
            "同时进行学习的用户数量",
            self.systemGroup
        )
        self.retryCard = RangeSettingCard(
            cfg.retryCount,
            FIF.SYNC,
            "重试次数",
            "学习失败时的最大重试次数",
            self.systemGroup
        )
        self.delayCard = RangeSettingCard(
            cfg.delayTime,
            FIF.PAUSE_BOLD,
            "延迟时间",
            "操作间隔延迟时间（秒）",
            self.systemGroup
        )

        # 浏览器设置组
        self.browserGroup = SettingCardGroup("浏览器设置", self.scrollWidget)
        self.headlessCard = SwitchSettingCard(
            FIF.VIEW,
            "无头模式",
            "启用无头模式，浏览器在后台运行",
            cfg.headless,
            self.browserGroup
        )
        self.browserCard = OptionsSettingCard(
            cfg.browserType,
            FIF.GLOBE,
            "浏览器类型",
            "选择用于自动化的浏览器",
            texts=["Chromium", "Chrome", "Firefox", "Edge"],
            parent=self.browserGroup
        )

        # OCR设置组
        self.ocrGroup = SettingCardGroup("OCR设置", self.scrollWidget)
        self.ocrEngineCard = OptionsSettingCard(
            cfg.ocrEngine,
            FIF.PHOTO,
            "OCR引擎",
            "选择验证码识别引擎",
            texts=["ddddocr", "baidu"],
            parent=self.ocrGroup
        )

        # 学习设置组
        self.learningGroup = SettingCardGroup("学习设置", self.scrollWidget)
        self.compulsoryCard = RangeSettingCard(
            cfg.compulsoryCourses,
            FIF.EDUCATION,
            "必修课程数量",
            "需要学习的必修课程数量",
            self.learningGroup
        )
        self.electiveCard = RangeSettingCard(
            cfg.electiveCourses,
            FIF.LIBRARY,
            "选修课程数量",
            "需要学习的选修课程数量",
            self.learningGroup
        )
        self.autoElectiveCard = SwitchSettingCard(
            FIF.ADD,
            "自动添加选修课",
            "自动添加选修课程到学习列表",
            cfg.autoAddElective,
            self.learningGroup
        )
        self.monitorIntervalCard = RangeSettingCard(
            cfg.monitorInterval,
            FIF.STOP_WATCH,
            "监控间隔",
            "视频学习监控检查间隔（秒）",
            self.learningGroup
        )

        self.__initWidget()

    def __initWidget(self):
        """
        初始化界面组件
        """
        self.resize(1000, 800)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setViewportMargins(0, 80, 0, 0)
        self.setWidget(self.scrollWidget)
        self.setWidgetResizable(True)
        self.setObjectName('studySettingInterface')

        # 初始化样式表
        self.scrollWidget.setObjectName('scrollWidget')
        self.settingLabel.setObjectName('settingLabel')

        # 初始化布局
        self.__initLayout()
        self.__connectSignalToSlot()

    def __initLayout(self):
        """
        初始化布局
        """
        self.settingLabel.move(36, 30)

        # 将卡片添加到组中
        self.systemGroup.addSettingCard(self.asyncLoginCard)
        self.systemGroup.addSettingCard(self.concurrentCard)
        self.systemGroup.addSettingCard(self.retryCard)
        self.systemGroup.addSettingCard(self.delayCard)

        self.browserGroup.addSettingCard(self.headlessCard)
        self.browserGroup.addSettingCard(self.browserCard)

        self.ocrGroup.addSettingCard(self.ocrEngineCard)

        self.learningGroup.addSettingCard(self.compulsoryCard)
        self.learningGroup.addSettingCard(self.electiveCard)
        self.learningGroup.addSettingCard(self.autoElectiveCard)
        self.learningGroup.addSettingCard(self.monitorIntervalCard)

        # 将设置卡片组添加到布局中
        self.expandLayout.setSpacing(28)
        self.expandLayout.setContentsMargins(36, 10, 36, 0)
        self.expandLayout.addWidget(self.systemGroup)
        self.expandLayout.addWidget(self.browserGroup)
        self.expandLayout.addWidget(self.ocrGroup)
        self.expandLayout.addWidget(self.learningGroup)

    def __showRestartTooltip(self):
        """
        显示重启提示
        """
        InfoBar.success(
            "更新成功",
            "配置将在重启后生效",
            duration=1500,
            parent=self
        )

    def __connectSignalToSlot(self):
        """
        连接信号到槽函数
        """
        cfg.appRestartSig.connect(self.__showRestartTooltip)